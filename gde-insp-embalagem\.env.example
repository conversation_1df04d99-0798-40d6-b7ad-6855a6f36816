# RabbitMQ
RABBITMQ_USER=admin
RABBITMQ_PASS=admin
RABBITMQ_PORT=5672
RABBITMQ_EXTERNAL_PORT=5672
RABBITMQ_MANAGEMENT_PORT=15672
RABBITMQ_MANAGEMENT_EXTERNAL_PORT=15672
RABBITMQ_URL=amqp://${RABBITMQ_USER}:${RABBITMQ_PASS}@localhost:${RABBITMQ_PORT}

# PostgreSQL
POSTGRES_USER=postgres
POSTGRES_PASSWORD=123456
POSTGRES_DB=gdeembalagem
POSTGRES_PORT=5432
POSTGRES_EXTERNAL_PORT=5432
DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@localhost:${POSTGRES_PORT}/${POSTGRES_DB}?schema=public

IMAGES_DIR="/home/<USER>/Projeto/gde_back/logs"

JERP_TOKEN=""

JERP_API="http://localhost:8082"

NEXT_PUBLIC_SOCKET_URL="http://localhost:3001"