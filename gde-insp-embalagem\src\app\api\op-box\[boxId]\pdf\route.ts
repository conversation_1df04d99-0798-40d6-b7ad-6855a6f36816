import { getTagPdf } from "@/app/op/[opId]/actions";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  req: NextRequest,
  { params }: { params: { boxId: string } }
) {
  try {
    const pdfBase64 = await getTagPdf(params.boxId);
    
    if (!pdfBase64) {
      return NextResponse.json(
        { error: "PDF não encontrado para esta caixa" },
        { status: 404 }
      );
    }

    return NextResponse.json({ pdfBase64 });
  } catch (error) {
    console.error("Erro ao buscar PDF:", error);
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    );
  }
}
