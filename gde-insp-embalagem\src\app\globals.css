@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}


.tag-area {
  padding: 10px 20px;
  width: 10cm;
  height: 5cm;
  background-color: white;
  color: #000;
  font-size: 16px;
  gap: 8px;
  box-sizing: border-box;
  font-family: Arial, Helvetica, sans-serif;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
}

.title {
  text-transform: uppercase;
  font-weight: bold;
}

.description {
  font-size: 11px;
}

.batch {
  text-transform: uppercase;
  font-weight: bold;
}

.barcode-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.barcode {
  font-size: 44px;
  flex: auto;
  text-align: center;
}

.quantity {
  margin-top: 25px;
  text-transform: uppercase;
  white-space: nowrap;
  font-size: 12px;
  font-weight: bold;
}

.no-warp-line {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}


::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: var(--radius);
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--primary) / 0.7);
  border-radius: var(--radius);
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}

/* For Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--primary) / 0.7) hsl(var(--muted));
}

.bg-red-600 {
  background-color: #dc2626;
}
.bg-blue-600 {
  background-color: #0e66e9;
}
.bg-green-600 {
  background-color: #2fad08;
}
.bg-black-600 {
  background-color: #111827;
}